import { PageAttributeNames } from 'app-constants';
import {
  AddCompanyDetailsField,
  SmallLoanContactExtraForm,
  SpouseSendInstructions,
} from 'components/contact-extra-form';
import { useContactExtraPageLogicSmallLoan } from 'hooks/page-logic/small-loan/use-contact-extra-page-logic-small-loan';

const ContactExtraPage = () => {
  const {
    form,
    onContactExtraFormSubmit,
    visiblePageAttributes,
    userInfoExtraValidationErrors,
    sendingConsentLinkValidationErrors,
    instructionsSent,
    setInstructionsSent,
    sendingConsentLink,
    occupationCategoryOptions,
    legalPeopleOptions,
    employmentDateOptions,
    addLegalPersonToInvoiceDisabled,
    legalPeopleLoading,
    handleSendConsentLink,
    onAddLegalPersonToInvoiceChange,
  } = useContactExtraPageLogicSmallLoan();

  return (
    <SmallLoanContactExtraForm
      form={form}
      onSubmit={onContactExtraFormSubmit}
      visiblePageAttributes={visiblePageAttributes}
      validationErrors={userInfoExtraValidationErrors}
      occupationCategoryOptions={occupationCategoryOptions}
      employmentDateOptions={employmentDateOptions}
      isSubmitting={form.formState.isSubmitting}
    >
      <AddCompanyDetailsField
        form={form}
        isDisabled={addLegalPersonToInvoiceDisabled}
        legalPeopleLoading={legalPeopleLoading}
        onChange={onAddLegalPersonToInvoiceChange}
        selectOptions={legalPeopleOptions}
        visible={
          visiblePageAttributes[PageAttributeNames.addLegalPersonToInvoice]
        }
      />

      <SpouseSendInstructions
        form={form}
        onSendInstructions={handleSendConsentLink}
        sendingConsentLink={sendingConsentLink}
        validationErrors={sendingConsentLinkValidationErrors}
        instructionsSent={instructionsSent}
        onInstructionsSentChange={setInstructionsSent}
        visible={
          visiblePageAttributes[PageAttributeNames.spouseInstructionsSection]
        }
      />
    </SmallLoanContactExtraForm>
  );
};

export default ContactExtraPage;
