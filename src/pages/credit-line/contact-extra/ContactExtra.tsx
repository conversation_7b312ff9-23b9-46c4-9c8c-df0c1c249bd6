import { CreditLineContactExtraForm } from 'components/contact-extra-form';
import { useRootContext } from 'context/root';
import { useContactExtraPageLogicCreditLine } from 'hooks/page-logic/credit-line';

const ContactExtraPage = () => {
  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const {
    onContactExtraFormSubmit,
    visiblePageAttributes,
    userInfoExtraValidationErrors,
    occupationCategoryOptions,
    employmentDateOptions,
    form,
  } = useContactExtraPageLogicCreditLine();

  return (
    <CreditLineContactExtraForm
      form={form}
      onSubmit={onContactExtraFormSubmit}
      visiblePageAttributes={visiblePageAttributes}
      validationErrors={userInfoExtraValidationErrors}
      occupationCategoryOptions={occupationCategoryOptions}
      employmentDateOptions={employmentDateOptions}
      isSubmitting={form.formState.isSubmitting}
      isNavigating={pageUrlAndNavigationProcessing}
      onBack={() => getPageUrlAndNavigate(false)}
    />
  );
};

export default ContactExtraPage;
