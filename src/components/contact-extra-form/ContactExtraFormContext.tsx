import type { Option } from 'models';
import { createContext, useContext } from 'react';

export interface ContactExtraFormContextValue {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  occupationCategoryOptions: Option[];
  employmentDateOptions: Option[];
}

const ContactExtraFormContext = createContext<ContactExtraFormContextValue | null>(null);

export const ContactExtraFormProvider = ContactExtraFormContext.Provider;

export const useContactExtraFormContext = () => {
  const context = useContext(ContactExtraFormContext);
  if (!context) {
    throw new Error('useContactExtraFormContext must be used within ContactExtraFormProvider');
  }
  return context;
};
