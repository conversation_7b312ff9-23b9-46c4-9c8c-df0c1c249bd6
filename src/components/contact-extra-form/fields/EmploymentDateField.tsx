import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormSelectField } from 'components/form/form-select-field';
import type { Option } from 'models';
import type { FieldValues } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface EmploymentDateFieldProps {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  employmentDateOptions: Option[];
  isSubmitting?: boolean;
}

export const EmploymentDateField = <TFormData extends FieldValues>({
  visiblePageAttributes,
  validationErrors,
  employmentDateOptions,
  isSubmitting = false,
}: EmploymentDateFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<TFormData>();

  if (!visiblePageAttributes[PageAttributeNames.employmentDate]) {
    return null;
  }

  return (
    <FormSelectField<TFormData>
      control={control}
      name={FormFieldNames.employmentDate as keyof TFormData}
      label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.employmentDateLabel)}
      options={employmentDateOptions}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.employmentDate] ||
        !!getFieldState(FormFieldNames.employmentDate as keyof TFormData).error
      }
    />
  );
};
