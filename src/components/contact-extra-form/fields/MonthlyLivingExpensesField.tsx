import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { FieldValues } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface MonthlyLivingExpensesFieldProps {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  isSubmitting?: boolean;
}

export const MonthlyLivingExpensesField = <TFormData extends FieldValues>({
  visiblePageAttributes,
  validationErrors,
  isSubmitting = false,
}: MonthlyLivingExpensesFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<TFormData>();

  if (!visiblePageAttributes[PageAttributeNames.monthlyLivingExpenses]) {
    return null;
  }

  return (
    <FormNumberInputField<TFormData>
      control={control}
      disabled={isSubmitting}
      name={FormFieldNames.monthlyLivingExpenses as keyof TFormData}
      label={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.livingExpensesPerMonthLabel,
      )}
      info={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.livingExpensesPerMonthTooltipLabel,
      )}
      invalid={
        validationErrors[FormFieldNames.monthlyLivingExpenses] ||
        !!getFieldState(FormFieldNames.monthlyLivingExpenses as keyof TFormData).error
      }
      suffix={'€'}
    />
  );
};
