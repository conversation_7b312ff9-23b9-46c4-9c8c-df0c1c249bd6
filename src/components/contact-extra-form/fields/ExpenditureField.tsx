import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface ExpenditureFieldProps {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  isSubmitting?: boolean;
}

export const ExpenditureField = <TFormData extends FieldValues>({
  visiblePageAttributes,
  validationErrors,
  isSubmitting = false,
}: ExpenditureFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<TFormData>();

  if (!visiblePageAttributes[PageAttributeNames.expenditureMonthly]) {
    return null;
  }

  return (
    <FormNumberInputField<TFormData>
      control={control}
      disabled={isSubmitting}
      name={FormFieldNames.expenditureMonthly as Path<TFormData>}
      label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureLabel)}
      info={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureTooltipLabel)}
      invalid={
        validationErrors[FormFieldNames.expenditureMonthly] ||
        !!getFieldState(FormFieldNames.expenditureMonthly as Path<TFormData>)
          .error
      }
      suffix={'€'}
    />
  );
};
