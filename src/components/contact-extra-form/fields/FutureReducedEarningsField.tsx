import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { CheckboxWrapper } from 'components/checkbox-wrapper';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface FutureReducedEarningsFieldProps {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  isSubmitting?: boolean;
}

export const FutureReducedEarningsField = <TFormData extends FieldValues>({
  visiblePageAttributes,
  validationErrors,
  isSubmitting = false,
}: FutureReducedEarningsFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState, watch, setValue } =
    useFormContext<TFormData>();

  if (!visiblePageAttributes[PageAttributeNames.futureReducedEarnings]) {
    return null;
  }

  return (
    <CheckboxWrapper
      className="mt-2"
      checked={
        !!watch(FormFieldNames.futureReducedEarnings as Path<TFormData>) ||
        validationErrors[FormFieldNames.futureReducedEarnings]
      }
      onCheckedChange={(checked) => {
        if (!checked) {
          setValue(
            FormFieldNames.futureReducedEarnings as Path<TFormData>,
            null as TFormData[Path<TFormData>],
          );
        }
      }}
      label={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsCheckboxLabel,
      )}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.futureReducedEarnings] ||
        !!getFieldState(FormFieldNames.futureReducedEarnings as Path<TFormData>)
          .error
      }
    >
      <FormNumberInputField<TFormData>
        control={control}
        disabled={isSubmitting}
        name={FormFieldNames.futureReducedEarnings as Path<TFormData>}
        label={t(
          LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsLabel,
        )}
        invalid={
          validationErrors[FormFieldNames.futureReducedEarnings] ||
          !!getFieldState(
            FormFieldNames.futureReducedEarnings as Path<TFormData>,
          ).error
        }
        suffix={'€'}
      />
    </CheckboxWrapper>
  );
};
