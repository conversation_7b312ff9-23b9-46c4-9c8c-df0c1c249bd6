import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { FieldValues } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface NumberOfDependentsFieldProps {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  isSubmitting?: boolean;
}

export const NumberOfDependentsField = <TFormData extends FieldValues>({
  visiblePageAttributes,
  validationErrors,
  isSubmitting = false,
}: NumberOfDependentsFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<TFormData>();

  if (!visiblePageAttributes[PageAttributeNames.numberOfDependents]) {
    return null;
  }

  return (
    <FormNumberInputField<TFormData>
      control={control}
      name={FormFieldNames.numberOfDependents as keyof TFormData}
      label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.dependentsLabel)}
      allowLeadingZeros
      fixedDecimalScale={true}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.numberOfDependents] ||
        !!getFieldState(FormFieldNames.numberOfDependents as keyof TFormData).error
      }
    />
  );
};
