import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { CheckboxWrapper } from 'components/checkbox-wrapper';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { FieldValues } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface PlanningNewDebtsFieldProps {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  isSubmitting?: boolean;
}

export const PlanningNewDebtsField = <TFormData extends FieldValues>({
  visiblePageAttributes,
  validationErrors,
  isSubmitting = false,
}: PlanningNewDebtsFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState, watch, setValue } = useFormContext<TFormData>();

  if (!visiblePageAttributes[PageAttributeNames.planningNewDebts]) {
    return null;
  }

  return (
    <CheckboxWrapper
      className="mt-2"
      checked={
        !!watch(FormFieldNames.planningNewDebts as keyof TFormData) ||
        validationErrors[FormFieldNames.planningNewDebts]
      }
      onCheckedChange={(checked) => {
        if (!checked) {
          setValue(FormFieldNames.planningNewDebts as keyof TFormData, null as any);
        }
      }}
      label={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsCheckboxLabel,
      )}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.planningNewDebts] ||
        !!getFieldState(FormFieldNames.planningNewDebts as keyof TFormData).error
      }
    >
      <FormNumberInputField<TFormData>
        control={control}
        name={FormFieldNames.planningNewDebts as keyof TFormData}
        label={t(
          LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsLabel,
        )}
        disabled={isSubmitting}
        invalid={
          validationErrors[FormFieldNames.planningNewDebts] ||
          !!getFieldState(FormFieldNames.planningNewDebts as keyof TFormData).error
        }
        suffix={'€'}
      />
    </CheckboxWrapper>
  );
};
