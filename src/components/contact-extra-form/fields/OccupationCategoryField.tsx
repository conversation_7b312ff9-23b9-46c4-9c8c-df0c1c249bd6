import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormSelectField } from 'components/form/form-select-field';
import type { Option } from 'models';
import type { FieldValues } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface OccupationCategoryFieldProps {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  occupationCategoryOptions: Option[];
  isSubmitting?: boolean;
}

export const OccupationCategoryField = <TFormData extends FieldValues>({
  visiblePageAttributes,
  validationErrors,
  occupationCategoryOptions,
  isSubmitting = false,
}: OccupationCategoryFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<TFormData>();

  if (!visiblePageAttributes[PageAttributeNames.occupationCategoryDropdown]) {
    return null;
  }

  return (
    <FormSelectField<TFormData>
      control={control}
      name={FormFieldNames.occupationCategory as keyof TFormData}
      label={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.occupationCategoryFieldLabel,
      )}
      options={occupationCategoryOptions}
      invalid={
        validationErrors[FormFieldNames.occupationCategory] ||
        !!getFieldState(FormFieldNames.occupationCategory as keyof TFormData).error
      }
      disabled={isSubmitting || !occupationCategoryOptions.length}
    />
  );
};
