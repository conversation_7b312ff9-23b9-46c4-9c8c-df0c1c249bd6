import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { CheckboxWrapper } from 'components/checkbox-wrapper';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import type { FieldValues } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface OverdueDebtFieldProps {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  isSubmitting?: boolean;
}

export const OverdueDebtField = <TFormData extends FieldValues>({
  visiblePageAttributes,
  validationErrors,
  isSubmitting = false,
}: OverdueDebtFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState, watch, setValue } = useFormContext<TFormData>();

  if (!visiblePageAttributes[PageAttributeNames.overdueDebt]) {
    return null;
  }

  return (
    <CheckboxWrapper
      className="mt-2"
      checked={
        !!watch(FormFieldNames.overdueDebt as keyof TFormData) ||
        validationErrors[FormFieldNames.overdueDebt]
      }
      onCheckedChange={(checked) => {
        if (!checked) {
          setValue(FormFieldNames.overdueDebt as keyof TFormData, null as any);
        }
      }}
      label={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtCheckboxLabel,
      )}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.overdueDebt] ||
        !!getFieldState(FormFieldNames.overdueDebt as keyof TFormData).error
      }
    >
      <FormNumberInputField<TFormData>
        control={control}
        name={FormFieldNames.overdueDebt as keyof TFormData}
        label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtLabel)}
        info={t(
          LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtTooltipLabel,
        )}
        disabled={isSubmitting}
        invalid={
          validationErrors[FormFieldNames.overdueDebt] ||
          !!getFieldState(FormFieldNames.overdueDebt as keyof TFormData).error
        }
        suffix={'€'}
      />
    </CheckboxWrapper>
  );
};
