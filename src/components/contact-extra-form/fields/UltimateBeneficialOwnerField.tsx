import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import type { FieldValues } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface UltimateBeneficialOwnerFieldProps {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  isSubmitting?: boolean;
}

export const UltimateBeneficialOwnerField = <TFormData extends FieldValues>({
  visiblePageAttributes,
  validationErrors,
  isSubmitting = false,
}: UltimateBeneficialOwnerFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<TFormData>();

  if (!visiblePageAttributes[PageAttributeNames.ultimateBeneficialOwner]) {
    return null;
  }

  return (
    <FormCheckboxField<TFormData>
      containerClassName="mt-2 px-2.5"
      control={control}
      label={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerLabel,
      )}
      name={FormFieldNames.ultimateBeneficialOwner as keyof TFormData}
      info={t(
        LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerTooltip,
      )}
      disabled={isSubmitting}
      invalid={
        validationErrors[FormFieldNames.ultimateBeneficialOwner] ||
        !!getFieldState(FormFieldNames.ultimateBeneficialOwner as keyof TFormData).error
      }
    />
  );
};
