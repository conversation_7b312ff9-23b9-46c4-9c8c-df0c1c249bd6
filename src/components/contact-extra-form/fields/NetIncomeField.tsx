import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import { isLtRegion } from 'environment';
import type { FieldValues, Path } from 'react-hook-form';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface NetIncomeFieldProps {
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  isSubmitting?: boolean;
}

export const NetIncomeField = <TFormData extends FieldValues>({
  visiblePageAttributes,
  validationErrors,
  isSubmitting = false,
}: NetIncomeFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext<TFormData>();

  if (!visiblePageAttributes[PageAttributeNames.netIncomeMonthly]) {
    return null;
  }

  return (
    <FormNumberInputField<TFormData>
      control={control}
      disabled={isSubmitting}
      name={FormFieldNames.netIncomeMonthly as Path<TFormData>}
      label={t(
        isLtRegion
          ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabelLt
          : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabel,
      )}
      info={t(
        isLtRegion
          ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabelLt
          : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabel,
      )}
      invalid={
        validationErrors[FormFieldNames.netIncomeMonthly] ||
        !!getFieldState(FormFieldNames.netIncomeMonthly as Path<TFormData>)
          .error
      }
      suffix={'€'}
    />
  );
};
