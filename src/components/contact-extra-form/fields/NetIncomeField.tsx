import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import { isLtRegion } from 'environment';
import { useFormContext, useFormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useContactExtraFormContext } from '../ContactExtraFormContext';

export const NetIncomeField = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { control, getFieldState } = useFormContext();
  const { isSubmitting } = useFormState({ control });
  const { visiblePageAttributes, validationErrors } =
    useContactExtraFormContext();

  if (!visiblePageAttributes[PageAttributeNames.netIncomeMonthly]) {
    return null;
  }

  return (
    <FormNumberInputField
      control={control}
      disabled={isSubmitting}
      name={FormFieldNames.netIncomeMonthly}
      label={t(
        isLtRegion
          ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabelLt
          : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabel,
      )}
      info={t(
        isLtRegion
          ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabelLt
          : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabel,
      )}
      invalid={
        validationErrors[FormFieldNames.netIncomeMonthly] ||
        !!getFieldState(FormFieldNames.netIncomeMonthly).error
      }
      suffix={'€'}
    />
  );
};
