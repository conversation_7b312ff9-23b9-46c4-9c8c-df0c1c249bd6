import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { WarningNotification } from 'components/notification';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import type { Option } from 'models';
import type { ReactNode } from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  NetIncomeField,
  ExpenditureField,
  MonthlyLivingExpensesField,
  NumberOfDependentsField,
  OccupationCategoryField,
  EmploymentDateField,
  OverdueDebtField,
  PlanningNewDebtsField,
  FutureReducedEarningsField,
  UltimateBeneficialOwnerField,
} from './fields';

export interface CreditLineContactExtraFormProps<TFormData extends FieldValues> {
  form: UseFormReturn<TFormData, any>;
  onSubmit: (data: TFormData) => void | Promise<void>;
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  occupationCategoryOptions: Option[];
  employmentDateOptions: Option[];
  isSubmitting?: boolean;
  isNavigating?: boolean;
  onBack?: () => void;
  children?: ReactNode;
  className?: string;
}

export const CreditLineContactExtraForm = <TFormData extends FieldValues>({
  form,
  onSubmit,
  visiblePageAttributes,
  validationErrors,
  occupationCategoryOptions,
  employmentDateOptions,
  isSubmitting = false,
  isNavigating = false,
  onBack,
  children,
  className,
}: CreditLineContactExtraFormProps<TFormData>) => {
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={`grid w-full gap-2 ${className || ''}`}
      >
        {visiblePageAttributes[PageAttributeNames.checkIncomeDisclaimer] ? (
          <WarningNotification className="mb-10">
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.checkIncomeDisclaimer2)}
          </WarningNotification>
        ) : null}

        <OccupationCategoryField<TFormData>
          visiblePageAttributes={visiblePageAttributes}
          validationErrors={validationErrors}
          occupationCategoryOptions={occupationCategoryOptions}
          isSubmitting={isSubmitting}
        />

        <NetIncomeField<TFormData>
          visiblePageAttributes={visiblePageAttributes}
          validationErrors={validationErrors}
          isSubmitting={isSubmitting}
        />

        <ExpenditureField<TFormData>
          visiblePageAttributes={visiblePageAttributes}
          validationErrors={validationErrors}
          isSubmitting={isSubmitting}
        />

        <MonthlyLivingExpensesField<TFormData>
          visiblePageAttributes={visiblePageAttributes}
          validationErrors={validationErrors}
          isSubmitting={isSubmitting}
        />

        <NumberOfDependentsField<TFormData>
          visiblePageAttributes={visiblePageAttributes}
          validationErrors={validationErrors}
          isSubmitting={isSubmitting}
        />

        <EmploymentDateField<TFormData>
          visiblePageAttributes={visiblePageAttributes}
          validationErrors={validationErrors}
          employmentDateOptions={employmentDateOptions}
          isSubmitting={isSubmitting}
        />

        <OverdueDebtField<TFormData>
          visiblePageAttributes={visiblePageAttributes}
          validationErrors={validationErrors}
          isSubmitting={isSubmitting}
        />

        <PlanningNewDebtsField<TFormData>
          visiblePageAttributes={visiblePageAttributes}
          validationErrors={validationErrors}
          isSubmitting={isSubmitting}
        />

        <FutureReducedEarningsField<TFormData>
          visiblePageAttributes={visiblePageAttributes}
          validationErrors={validationErrors}
          isSubmitting={isSubmitting}
        />

        <UltimateBeneficialOwnerField<TFormData>
          visiblePageAttributes={visiblePageAttributes}
          validationErrors={validationErrors}
          isSubmitting={isSubmitting}
        />

        {children}

        <Button
          className="mt-12"
          disabled={!isSubmitting && isNavigating}
          loading={isSubmitting}
          type="submit"
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>

        {onBack && (
          <Button
            fullWidth
            className="mt-2"
            variant="white"
            loading={!isSubmitting && isNavigating}
            disabled={isSubmitting}
            onClick={onBack}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
          </Button>
        )}
      </form>
    </Form>
  );
};
