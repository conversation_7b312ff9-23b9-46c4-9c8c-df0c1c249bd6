import { wrapUseRoutesV6 } from '@sentry/react';
import {
  AppRoutePaths,
  AppRouterParams,
  CreditLimitRecalculationRoutePaths,
  CreditLineRoutePaths,
  CreditLineWithdrawalRoutePaths,
  EPARAKSTS_LOGIN_PAGE_PATH,
  ERROR_PAGE_PATH,
  HirePurchaseRoutePaths,
  IncomeInsuranceRoutePaths,
  ROOT_PAGE_PATH,
  SmallLoanRoutePaths,
  UNKNOWN_PAGE_ROUTE_PATH,
} from 'app-constants';
import { PageUrlCheckRender } from 'components/page-url-check-render';
import { useShouldShowSchedulePage } from 'hooks';
import { IncomeInsuranceLoginPage } from 'pages/income-insurance/login';
import { memo } from 'react';
import { useRoutes } from 'react-router-dom';

import { AuthInsuranceHashPageUrlCheckRender } from './AuthInsuranceHashPageUrlCheckRender';
import { AuthRedirect } from './AuthRedirect';
import { CreditLineRouteWithPageUrlCheck } from './CreditLineRouteWithPageUrlCheck';
import pages from './lazy-pages';
import { PageUrlCheckedRoute } from './PageUrlCheckedRoute';
import { Redirect } from './Redirect';

const {
  // Root pages
  CreditLineRootPage,
  IncomeInsuranceRootPage,
  SmallLoanRootPage,
  // Schedule pages
  HirePurchaseSchedulePage,
  // Checkout pages
  HirePurchaseCheckoutPage,
  SmallLoanCheckoutPage,
  CreditLineWithdrawalCheckoutPage,
  CreditLimitRecalculationCheckoutPage,
  CreditLineCheckoutPage,
  // Login pages
  HirePurchaseLoginPage,
  SmallLoanLoginPage,
  CreditLineLoginPage,
  CreditLineWithdrawalLoginPage,
  CreditLimitRecalculationLoginPage,
  // Contact pages
  HirePurchaseContactPage,
  SmallLoanContactPage,
  CreditLineContactPage,
  CreditLimitRecalculationContactPage,
  CreditLineWithdrawalContactPage,
  // Purpose of loan pages
  HirePurchasePurposeOfLoanPage,
  SmallLoanPurposeOfLoanPage,
  CreditLinePurposeOfLoanPage,
  CreditLimitRecalculationPurposeOfLoanPage,
  CreditLineWithdrawalPurposeOfLoanPage,
  // Contact extra pages
  HirePurchaseContactExtraPage,
  SmallLoanContactExtraPage,
  CreditLineContactExtraPage,
  CreditLimitRecalculationContactExtraPage,
  CreditLineWithdrawalContactExtraPage,
  // Account scoring pages
  HirePurchaseAccountScoringPage,
  SmallLoanAccountScoringPage,
  CreditLineAccountScoringPage,
  CreditLimitRecalculationAccountScoringPage,
  CreditLineWithdrawalAccountScoringPage,
  // EMTA consent pages
  HirePurchaseEmtaConsentPage,
  SmallLoanEmtaConsentPage,
  CreditLineEmtaConsentPage,
  CreditLimitRecalculationEmtaConsentPage,
  CreditLineWithdrawalEmtaConsentPage,
  // Income verification pages
  CreditLineIncomeVerificationPage,
  CreditLineWithdrawalIncomeVerificationPage,
  CreditLimitRecalculationIncomeVerificationPage,
  HirePurchaseIncomeVerificationPage,
  SmallLoanIncomeVerificationPage,
  // Spouse consent pages
  HirePurchaseSpouseConsentPage,
  SmallLoanSpouseConsentPage,
  CreditLineSpouseConsentPage,
  CreditLimitRecalculationSpouseConsentPage,
  CreditLineWithdrawalSpouseConsentPage,
  // Signing pages
  HirePurchaseSigningPage,
  SmallLoanSigningPage,
  CreditLineSigningPage,
  IncomeInsuranceSigningPage,
  // Success pages
  HirePurchaseSuccessPage,
  SmallLoanSuccessPage,
  CreditLineSuccessPage,
  IncomeInsuranceSuccessPage,
  // Reject pages
  HirePurchaseRejectPage,
  SmallLoanRejectPage,
  CreditLineRejectPage,
  IncomeInsuranceRejectPage,
  // Pending pages
  HirePurchasePendingPage,
  SmallLoanPendingPage,
  CreditLinePendingPage,
  CreditLimitRecalculationPendingPage,
  CreditLineWithdrawalPendingPage,
  // Error pages
  ErrorPage,
  ErrorPageV2,
  // Short link handler page
  ShortLinkHandlerPage,
  // EParaksts Login page
  EParakstsLoginPage,
  // Hire Purchase related pages
  AfterDirectPaymentPage,
  DirectPaymentPage,
} = pages;

const sharedRoutes = [
  {
    path: ERROR_PAGE_PATH,
    element: <ErrorPage />,
  },
  {
    path: UNKNOWN_PAGE_ROUTE_PATH,
    element: <Redirect to={`../${ERROR_PAGE_PATH}`} />,
  },
];

const sharedRoutesV2 = [
  {
    path: ERROR_PAGE_PATH,
    element: <ErrorPageV2 />,
  },
  {
    path: UNKNOWN_PAGE_ROUTE_PATH,
    element: <Redirect to={`../${ERROR_PAGE_PATH}`} />,
  },
];

const useSentryRoutes = wrapUseRoutesV6(useRoutes);

const AppRouter = memo(() => {
  const shouldNavigateToSchedulePage = useShouldShowSchedulePage();

  return useSentryRoutes([
    {
      path: `${AppRoutePaths.SHORT_LINK}/:${AppRouterParams.shortReferenceCode}`,
      element: <ShortLinkHandlerPage />,
    },
    {
      path: EPARAKSTS_LOGIN_PAGE_PATH,
      element: <EParakstsLoginPage />,
    },
    {
      path: AppRoutePaths.HIRE_PURCHASE,
      children: [
        {
          path: ROOT_PAGE_PATH,
          element: (
            <Redirect
              to={
                shouldNavigateToSchedulePage
                  ? HirePurchaseRoutePaths.SCHEDULE
                  : HirePurchaseRoutePaths.CHECKOUT
              }
            />
          ),
        },
        {
          path: HirePurchaseRoutePaths.SCHEDULE,
          element: <PageUrlCheckedRoute component={HirePurchaseSchedulePage} />,
        },
        {
          path: HirePurchaseRoutePaths.CHECKOUT,
          element: <PageUrlCheckedRoute component={HirePurchaseCheckoutPage} />,
        },
        {
          path: HirePurchaseRoutePaths.LOGIN,
          element: <PageUrlCheckedRoute component={HirePurchaseLoginPage} />,
        },
        {
          path: HirePurchaseRoutePaths.CONTACT,
          element: <PageUrlCheckedRoute component={HirePurchaseContactPage} />,
        },
        {
          path: HirePurchaseRoutePaths.PURPOSE_OF_LOAN,
          element: (
            <PageUrlCheckedRoute component={HirePurchasePurposeOfLoanPage} />
          ),
        },
        {
          path: HirePurchaseRoutePaths.CONTACT_EXTRA,
          element: (
            <PageUrlCheckedRoute component={HirePurchaseContactExtraPage} />
          ),
        },
        {
          path: HirePurchaseRoutePaths.SPOUSE,
          element: <HirePurchaseSpouseConsentPage />,
        },
        {
          path: HirePurchaseRoutePaths.ACCOUNT_SCORING,
          element: (
            <PageUrlCheckedRoute component={HirePurchaseAccountScoringPage} />
          ),
        },
        {
          path: HirePurchaseRoutePaths.INCOME_VERIFICATION,
          element: (
            <PageUrlCheckedRoute
              component={HirePurchaseIncomeVerificationPage}
            />
          ),
        },

        {
          path: HirePurchaseRoutePaths.EMTA_CONSENT,
          element: (
            <PageUrlCheckedRoute component={HirePurchaseEmtaConsentPage} />
          ),
        },
        {
          path: HirePurchaseRoutePaths.SIGNING,
          element: <PageUrlCheckedRoute component={HirePurchaseSigningPage} />,
        },
        {
          path: HirePurchaseRoutePaths.PENDING,
          element: <PageUrlCheckedRoute component={HirePurchasePendingPage} />,
        },
        {
          path: HirePurchaseRoutePaths.SUCCESS,
          element: <PageUrlCheckedRoute component={HirePurchaseSuccessPage} />,
        },
        {
          path: HirePurchaseRoutePaths.DIRECT_PAYMENT,
          element: <DirectPaymentPage />,
        },
        {
          path: HirePurchaseRoutePaths.AFTER_DIRECT_PAYMENT,
          element: <AfterDirectPaymentPage />,
        },
        {
          path: HirePurchaseRoutePaths.AFTER_DIRECT_CARD_PAYMENT,
          element: <AfterDirectPaymentPage />,
        },
        {
          path: HirePurchaseRoutePaths.REJECT,
          element: <PageUrlCheckedRoute component={HirePurchaseRejectPage} />,
        },
        ...sharedRoutes,
      ],
    },
    {
      path: AppRoutePaths.SMALL_LOAN,
      element: <SmallLoanRootPage />,
      children: [
        {
          path: ROOT_PAGE_PATH,
          element: <Redirect to={SmallLoanRoutePaths.CHECKOUT} />,
        },
        {
          path: SmallLoanRoutePaths.CHECKOUT,
          element: (
            <PageUrlCheckRender>
              <SmallLoanCheckoutPage />
            </PageUrlCheckRender>
          ),
        },
        {
          path: SmallLoanRoutePaths.LOGIN,
          element: (
            <PageUrlCheckRender>
              <SmallLoanLoginPage />
            </PageUrlCheckRender>
          ),
        },
        {
          path: SmallLoanRoutePaths.CONTACT,
          element: (
            <PageUrlCheckRender>
              <SmallLoanContactPage />
            </PageUrlCheckRender>
          ),
        },
        {
          path: SmallLoanRoutePaths.PURPOSE_OF_LOAN,
          element: (
            <PageUrlCheckRender>
              <SmallLoanPurposeOfLoanPage />
            </PageUrlCheckRender>
          ),
        },
        {
          path: SmallLoanRoutePaths.CONTACT_EXTRA,
          element: (
            <PageUrlCheckedRoute component={SmallLoanContactExtraPage} />
          ),
        },
        {
          path: SmallLoanRoutePaths.SPOUSE,
          element: <SmallLoanSpouseConsentPage />,
        },
        {
          path: SmallLoanRoutePaths.ACCOUNT_SCORING,
          element: (
            <PageUrlCheckedRoute component={SmallLoanAccountScoringPage} />
          ),
        },
        {
          path: SmallLoanRoutePaths.INCOME_VERIFICATION,
          element: (
            <PageUrlCheckRender>
              <SmallLoanIncomeVerificationPage />
            </PageUrlCheckRender>
          ),
        },
        {
          path: SmallLoanRoutePaths.EMTA_CONSENT,
          element: <PageUrlCheckedRoute component={SmallLoanEmtaConsentPage} />,
        },
        {
          path: SmallLoanRoutePaths.SIGNING,
          element: (
            <PageUrlCheckRender>
              <SmallLoanSigningPage />
            </PageUrlCheckRender>
          ),
        },
        {
          path: SmallLoanRoutePaths.PENDING,
          element: (
            <PageUrlCheckRender>
              <SmallLoanPendingPage />
            </PageUrlCheckRender>
          ),
        },
        {
          path: SmallLoanRoutePaths.SUCCESS,
          element: <PageUrlCheckedRoute component={SmallLoanSuccessPage} />,
        },
        {
          path: SmallLoanRoutePaths.REJECT,
          element: <PageUrlCheckedRoute component={SmallLoanRejectPage} />,
        },
        {
          path: ERROR_PAGE_PATH,
          element: <ErrorPage />,
        },
        {
          path: UNKNOWN_PAGE_ROUTE_PATH,
          element: <Redirect to={`../${ERROR_PAGE_PATH}`} />,
        },
      ],
    },
    {
      path: AppRoutePaths.CREDIT_LINE,
      element: <CreditLineRootPage />,
      children: [
        {
          path: ROOT_PAGE_PATH,
          element: <Redirect to={CreditLineRoutePaths.CHECKOUT} />,
        },
        {
          path: CreditLineRoutePaths.CHECKOUT,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLineCheckoutPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.LOGIN,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLineLoginPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.CONTACT,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLineContactPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.PURPOSE_OF_LOAN,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLinePurposeOfLoanPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.CONTACT_EXTRA,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLineContactExtraPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.SPOUSE,
          element: <CreditLineSpouseConsentPage />,
        },
        {
          path: CreditLineRoutePaths.ACCOUNT_SCORING,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLineAccountScoringPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.INCOME_VERIFICATION,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLineIncomeVerificationPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.EMTA_CONSENT,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLineEmtaConsentPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.SIGNING,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLineSigningPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.PENDING,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLinePendingPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.SUCCESS,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLineSuccessPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        {
          path: CreditLineRoutePaths.REJECT,
          element: (
            <CreditLineRouteWithPageUrlCheck>
              <CreditLineRejectPage />
            </CreditLineRouteWithPageUrlCheck>
          ),
        },
        ...sharedRoutesV2,
      ],
    },
    {
      path: AppRoutePaths.CREDIT_LIMIT_RECALCULATION,
      children: [
        {
          path: ROOT_PAGE_PATH,
          element: (
            <Redirect to={CreditLimitRecalculationRoutePaths.CHECKOUT} />
          ),
        },
        {
          path: CreditLimitRecalculationRoutePaths.CHECKOUT,
          element: (
            <PageUrlCheckedRoute
              component={CreditLimitRecalculationCheckoutPage}
            />
          ),
        },
        {
          path: CreditLimitRecalculationRoutePaths.LOGIN,
          element: (
            <PageUrlCheckedRoute
              component={CreditLimitRecalculationLoginPage}
            />
          ),
        },
        {
          path: CreditLimitRecalculationRoutePaths.CONTACT,
          element: (
            <PageUrlCheckedRoute
              component={CreditLimitRecalculationContactPage}
            />
          ),
        },
        {
          path: CreditLimitRecalculationRoutePaths.PURPOSE_OF_LOAN,
          element: (
            <PageUrlCheckedRoute
              component={CreditLimitRecalculationPurposeOfLoanPage}
            />
          ),
        },
        {
          path: CreditLimitRecalculationRoutePaths.CONTACT_EXTRA,
          element: (
            <PageUrlCheckedRoute
              component={CreditLimitRecalculationContactExtraPage}
            />
          ),
        },
        {
          path: CreditLimitRecalculationRoutePaths.INCOME_VERIFICATION,
          element: (
            <PageUrlCheckedRoute
              component={CreditLimitRecalculationIncomeVerificationPage}
            />
          ),
        },
        {
          path: CreditLimitRecalculationRoutePaths.SPOUSE,
          element: <CreditLimitRecalculationSpouseConsentPage />,
        },
        {
          path: CreditLimitRecalculationRoutePaths.ACCOUNT_SCORING,
          element: (
            <PageUrlCheckedRoute
              component={CreditLimitRecalculationAccountScoringPage}
            />
          ),
        },
        {
          path: CreditLimitRecalculationRoutePaths.EMTA_CONSENT,
          element: (
            <PageUrlCheckedRoute
              component={CreditLimitRecalculationEmtaConsentPage}
            />
          ),
        },
        {
          path: CreditLimitRecalculationRoutePaths.PENDING,
          element: (
            <PageUrlCheckedRoute
              component={CreditLimitRecalculationPendingPage}
            />
          ),
        },
        ...sharedRoutes,
      ],
    },
    {
      path: AppRoutePaths.CREDIT_LINE_WITHDRAWAL,
      children: [
        {
          path: ROOT_PAGE_PATH,
          element: <Redirect to={CreditLineWithdrawalRoutePaths.CHECKOUT} />,
        },
        {
          path: CreditLineWithdrawalRoutePaths.CHECKOUT,
          element: (
            <PageUrlCheckedRoute component={CreditLineWithdrawalCheckoutPage} />
          ),
        },
        {
          path: CreditLineWithdrawalRoutePaths.LOGIN,
          element: (
            <PageUrlCheckedRoute component={CreditLineWithdrawalLoginPage} />
          ),
        },
        {
          path: CreditLineWithdrawalRoutePaths.CONTACT,
          element: (
            <PageUrlCheckedRoute component={CreditLineWithdrawalContactPage} />
          ),
        },
        {
          path: CreditLineWithdrawalRoutePaths.PURPOSE_OF_LOAN,
          element: (
            <PageUrlCheckedRoute
              component={CreditLineWithdrawalPurposeOfLoanPage}
            />
          ),
        },
        {
          path: CreditLineWithdrawalRoutePaths.CONTACT_EXTRA,
          element: (
            <PageUrlCheckedRoute
              component={CreditLineWithdrawalContactExtraPage}
            />
          ),
        },
        {
          path: CreditLineWithdrawalRoutePaths.SPOUSE,
          element: <CreditLineWithdrawalSpouseConsentPage />,
        },
        {
          path: CreditLineWithdrawalRoutePaths.ACCOUNT_SCORING,
          element: (
            <PageUrlCheckedRoute
              component={CreditLineWithdrawalAccountScoringPage}
            />
          ),
        },
        {
          path: CreditLineWithdrawalRoutePaths.INCOME_VERIFICATION,
          element: (
            <PageUrlCheckedRoute
              component={CreditLineWithdrawalIncomeVerificationPage}
            />
          ),
        },
        {
          path: CreditLineWithdrawalRoutePaths.EMTA_CONSENT,
          element: (
            <PageUrlCheckedRoute
              component={CreditLineWithdrawalEmtaConsentPage}
            />
          ),
        },
        {
          path: CreditLineWithdrawalRoutePaths.PENDING,
          element: (
            <PageUrlCheckedRoute component={CreditLineWithdrawalPendingPage} />
          ),
        },
        ...sharedRoutes,
      ],
    },
    {
      path: AppRoutePaths.INCOME_INSURANCE,
      element: <IncomeInsuranceRootPage />,
      children: [
        {
          path: ROOT_PAGE_PATH,
          element: (
            <AuthRedirect
              to={IncomeInsuranceRoutePaths.SIGNING}
              auth={IncomeInsuranceRoutePaths.LOGIN}
            />
          ),
        },
        {
          path: IncomeInsuranceRoutePaths.LOGIN,
          element: (
            <AuthInsuranceHashPageUrlCheckRender>
              <IncomeInsuranceLoginPage />
            </AuthInsuranceHashPageUrlCheckRender>
          ),
        },
        {
          path: IncomeInsuranceRoutePaths.SIGNING,
          element: (
            <AuthInsuranceHashPageUrlCheckRender>
              <IncomeInsuranceSigningPage />
            </AuthInsuranceHashPageUrlCheckRender>
          ),
        },
        {
          path: IncomeInsuranceRoutePaths.SUCCESS,
          element: (
            <AuthInsuranceHashPageUrlCheckRender>
              <IncomeInsuranceSuccessPage />
            </AuthInsuranceHashPageUrlCheckRender>
          ),
        },
        {
          path: IncomeInsuranceRoutePaths.REJECT,
          element: (
            <AuthInsuranceHashPageUrlCheckRender>
              <IncomeInsuranceRejectPage />
            </AuthInsuranceHashPageUrlCheckRender>
          ),
        },
      ],
      ...sharedRoutes,
    },
    ...sharedRoutes,
  ]);
});

export default AppRouter;
